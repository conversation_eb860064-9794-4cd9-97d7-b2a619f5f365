# Flask 文件下载服务器

一个简单易用的Flask文件下载服务器，允许用户浏览和下载指定目录下的所有文件。

## 功能特性

- 📁 浏览指定目录下的所有文件
- ⬇️ 单个文件下载
- 📦 打包下载所有文件（ZIP格式）
- 📊 显示文件大小和修改时间
- 🔒 安全的文件访问控制
- 📱 响应式设计，支持移动设备
- 🎨 美观的用户界面

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行服务器

```bash
python app.py
```

服务器将在 `http://localhost:5000` 启动。

### 3. 自定义下载目录

默认下载目录是 `./downloads`，你可以通过环境变量来修改：

```bash
# Windows
set DOWNLOAD_FOLDER=C:\path\to\your\files
python app.py

# Linux/Mac
export DOWNLOAD_FOLDER=/path/to/your/files
python app.py
```

## 使用方法

1. 将要分享的文件放入下载目录（默认为 `./downloads`）
2. 启动服务器
3. 在浏览器中访问 `http://localhost:5000`
4. 用户可以：
   - 查看所有可下载的文件列表
   - 点击单个文件进行下载
   - 点击"打包下载所有文件"按钮下载ZIP压缩包

## API接口

### 获取文件列表
```
GET /api/files
```

返回JSON格式的文件列表：
```json
{
  "files": [
    {
      "name": "example.txt",
      "size": 1024,
      "modified": "2023-12-01 10:30:00",
      "is_file": true
    }
  ],
  "total": 1
}
```

### 下载单个文件
```
GET /download/<filename>
```

### 下载所有文件（ZIP）
```
GET /download-all
```

## 安全特性

- 文件名安全检查，防止路径遍历攻击
- 只允许访问指定目录内的文件
- 自动处理文件名中的特殊字符

## 配置选项

- `DOWNLOAD_FOLDER`: 下载目录路径（环境变量）
- 服务器默认运行在 `0.0.0.0:5000`，可在代码中修改

## 注意事项

1. 确保下载目录存在且有读取权限
2. 大文件下载可能需要较长时间
3. ZIP打包功能会占用临时磁盘空间
4. 建议在生产环境中使用反向代理（如Nginx）

## 许可证

MIT License
