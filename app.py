#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask文件下载服务器
提供指定目录下文件的浏览和下载功能
"""

import os
import zipfile
import tempfile
from flask import Flask, render_template, send_file, request, abort, jsonify
from werkzeug.utils import secure_filename
import mimetypes
from datetime import datetime

app = Flask(__name__)

# 配置
DOWNLOAD_FOLDER = './downloads'  # 默认下载目录，可以通过环境变量修改
app.config['DOWNLOAD_FOLDER'] = os.environ.get('DOWNLOAD_FOLDER', DOWNLOAD_FOLDER)

def get_file_info(filepath):
    """获取文件信息"""
    try:
        stat = os.stat(filepath)
        return {
            'name': os.path.basename(filepath),
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
            'is_file': os.path.isfile(filepath)
        }
    except OSError:
        return None

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024.0 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f}{size_names[i]}"

@app.route('/')
def index():
    """主页 - 显示文件列表"""
    download_folder = app.config['DOWNLOAD_FOLDER']
    
    # 确保下载目录存在
    if not os.path.exists(download_folder):
        os.makedirs(download_folder)
        return render_template('index.html', files=[], folder=download_folder, message="下载目录已创建，请添加文件后刷新页面")
    
    files = []
    try:
        for filename in os.listdir(download_folder):
            filepath = os.path.join(download_folder, filename)
            file_info = get_file_info(filepath)
            if file_info and file_info['is_file']:
                file_info['size_formatted'] = format_file_size(file_info['size'])
                files.append(file_info)
        
        # 按文件名排序
        files.sort(key=lambda x: x['name'].lower())
        
    except OSError as e:
        return render_template('index.html', files=[], folder=download_folder, error=f"读取目录失败: {str(e)}")
    
    return render_template('index.html', files=files, folder=download_folder)

@app.route('/download/<filename>')
def download_file(filename):
    """下载单个文件"""
    # 安全检查文件名
    filename = secure_filename(filename)
    if not filename:
        abort(400, "无效的文件名")
    
    download_folder = app.config['DOWNLOAD_FOLDER']
    filepath = os.path.join(download_folder, filename)
    
    # 检查文件是否存在
    if not os.path.exists(filepath) or not os.path.isfile(filepath):
        abort(404, "文件不存在")
    
    # 检查文件是否在允许的目录内（防止路径遍历攻击）
    if not os.path.abspath(filepath).startswith(os.path.abspath(download_folder)):
        abort(403, "访问被拒绝")
    
    return send_file(filepath, as_attachment=True, download_name=filename)

@app.route('/download-all')
def download_all():
    """打包下载所有文件"""
    download_folder = app.config['DOWNLOAD_FOLDER']
    
    if not os.path.exists(download_folder):
        abort(404, "下载目录不存在")
    
    # 获取所有文件
    files = []
    for filename in os.listdir(download_folder):
        filepath = os.path.join(download_folder, filename)
        if os.path.isfile(filepath):
            files.append((filename, filepath))
    
    if not files:
        abort(404, "没有可下载的文件")
    
    # 创建临时zip文件
    temp_zip = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')
    try:
        with zipfile.ZipFile(temp_zip.name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for filename, filepath in files:
                zipf.write(filepath, filename)
        
        # 生成下载文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        zip_filename = f"files_{timestamp}.zip"
        
        return send_file(
            temp_zip.name,
            as_attachment=True,
            download_name=zip_filename,
            mimetype='application/zip'
        )
    except Exception as e:
        # 清理临时文件
        try:
            os.unlink(temp_zip.name)
        except:
            pass
        abort(500, f"创建压缩文件失败: {str(e)}")

@app.route('/api/files')
def api_files():
    """API接口 - 获取文件列表"""
    download_folder = app.config['DOWNLOAD_FOLDER']
    
    if not os.path.exists(download_folder):
        return jsonify({'error': '下载目录不存在', 'files': []})
    
    files = []
    try:
        for filename in os.listdir(download_folder):
            filepath = os.path.join(download_folder, filename)
            file_info = get_file_info(filepath)
            if file_info and file_info['is_file']:
                files.append(file_info)
        
        files.sort(key=lambda x: x['name'].lower())
        return jsonify({'files': files, 'total': len(files)})
        
    except OSError as e:
        return jsonify({'error': f'读取目录失败: {str(e)}', 'files': []})

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error="页面或文件未找到", code=404), 404

@app.errorhandler(403)
def forbidden(error):
    return render_template('error.html', error="访问被拒绝", code=403), 403

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error="服务器内部错误", code=500), 500

if __name__ == '__main__':
    # 创建下载目录
    download_folder = app.config['DOWNLOAD_FOLDER']
    if not os.path.exists(download_folder):
        os.makedirs(download_folder)
        print(f"创建下载目录: {os.path.abspath(download_folder)}")
    
    print(f"文件下载服务器启动")
    print(f"下载目录: {os.path.abspath(download_folder)}")
    print(f"访问地址: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
