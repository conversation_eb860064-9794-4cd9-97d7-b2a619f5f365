<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件下载服务器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .info-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .download-all {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn-primary {
            background: #2196F3;
        }
        
        .btn-primary:hover {
            background: #1976D2;
        }
        
        .file-list {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .file-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: 20px;
            align-items: center;
        }
        
        .file-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: 20px;
            align-items: center;
            transition: background-color 0.2s;
        }
        
        .file-item:hover {
            background-color: #f8f9fa;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-name {
            font-weight: 500;
            color: #333;
        }
        
        .file-size {
            color: #666;
            font-size: 0.9em;
        }
        
        .file-date {
            color: #666;
            font-size: 0.9em;
        }
        
        .download-btn {
            padding: 8px 16px;
            background: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .download-btn:hover {
            background: #1976D2;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            color: #ddd;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        @media (max-width: 768px) {
            .file-header, .file-item {
                grid-template-columns: 1fr auto;
                gap: 10px;
            }
            
            .file-size, .file-date {
                display: none;
            }
            
            .stats {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 文件下载服务器</h1>
            <p>浏览和下载 {{ folder }} 目录中的文件</p>
        </div>
        
        {% if message %}
        <div class="alert alert-info">
            {{ message }}
        </div>
        {% endif %}
        
        {% if error %}
        <div class="alert alert-error">
            {{ error }}
        </div>
        {% endif %}
        
        {% if files %}
        <div class="stats">
            <span><strong>{{ files|length }}</strong> 个文件可供下载</span>
            <div class="download-all">
                <a href="/download-all" class="btn">📦 打包下载所有文件</a>
            </div>
        </div>
        
        <div class="file-list">
            <div class="file-header">
                <div>文件名</div>
                <div>大小</div>
                <div>修改时间</div>
                <div>操作</div>
            </div>
            
            {% for file in files %}
            <div class="file-item">
                <div class="file-name">📄 {{ file.name }}</div>
                <div class="file-size">{{ file.size_formatted }}</div>
                <div class="file-date">{{ file.modified }}</div>
                <div>
                    <a href="/download/{{ file.name }}" class="download-btn">⬇️ 下载</a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="file-list">
            <div class="empty-state">
                <div style="font-size: 4em; margin-bottom: 20px;">📂</div>
                <h3>暂无文件</h3>
                <p>请在 <code>{{ folder }}</code> 目录中添加文件后刷新页面</p>
                <br>
                <button onclick="location.reload()" class="btn btn-primary">🔄 刷新页面</button>
            </div>
        </div>
        {% endif %}
    </div>
    
    <script>
        // 自动刷新功能（可选）
        function autoRefresh() {
            if (confirm('是否启用自动刷新？（每30秒检查一次新文件）')) {
                setInterval(() => {
                    location.reload();
                }, 30000);
            }
        }
        
        // 页面加载完成后询问是否启用自动刷新
        // window.onload = autoRefresh;
    </script>
</body>
</html>
